#!/usr/bin/env python3
"""
RBAC Database Initialization Script

This script initializes the admin database with the complete RBAC system including:
- Admin database creation
- Required collections setup
- RBAC roles and permissions
- Role hierarchy configuration
- Default users for testing

Usage:
    python scripts/init_rbac_database.py [--reset] [--create-users]
    
Arguments:
    --reset: Reset existing data before initialization
    --create-users: Create default test users for each role
"""

import sys
import os
import argparse
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.rbac_init import initialize_complete_rbac_system, create_tenant_with_rbac
from app.core.database import get_admin_db, get_async_client
from dotenv import load_dotenv

load_dotenv()

def reset_admin_database():
    """
    Reset the admin database by dropping it completely
    """
    try:
        client = get_async_client()
        project_name = os.getenv("PROJECT_NAME", "default").lower()
        admin_db_name = f"{project_name}_admin"
        
        print(f"⚠️  WARNING: This will completely delete the database '{admin_db_name}'")
        confirm = input("Are you sure you want to continue? (yes/no): ")
        
        if confirm.lower() != 'yes':
            print("❌ Operation cancelled")
            return False
        
        # Drop the database
        client.drop_database(admin_db_name)
        print(f"✅ Database '{admin_db_name}' has been reset")
        return True
        
    except Exception as e:
        print(f"❌ Error resetting database: {e}")
        return False

def check_database_status():
    """
    Check the current status of the admin and tenant databases
    """
    try:
        admin_db = get_admin_db()
        collections = admin_db.list_collection_names()

        print(f"📊 Admin Database Status:")
        print(f"   Database: {admin_db.name}")
        print(f"   Collections: {len(collections)}")

        if collections:
            print(f"   Existing collections: {', '.join(collections)}")

            # Check tenants
            if "tenants" in collections:
                tenant_count = admin_db.tenants.count_documents({})
                print(f"   tenants: {tenant_count} documents")

                # List tenant databases
                if tenant_count > 0:
                    print("\n📊 Tenant Databases:")
                    tenants = list(admin_db.tenants.find({}, {"name": 1, "slug": 1, "db_name": 1}))

                    client = get_async_client()
                    for tenant in tenants:
                        tenant_db_name = tenant["db_name"]
                        try:
                            tenant_db = client[tenant_db_name]
                            tenant_collections = tenant_db.list_collection_names()

                            print(f"   {tenant['name']} ({tenant['slug']}):")
                            print(f"     Database: {tenant_db_name}")
                            print(f"     Collections: {len(tenant_collections)}")

                            # Check counts in tenant database
                            for collection_name in ["users", "roles", "permissions", "config"]:
                                if collection_name in tenant_collections:
                                    count = tenant_db[collection_name].count_documents({})
                                    print(f"     {collection_name}: {count} documents")
                        except Exception as e:
                            print(f"     Error accessing tenant database: {e}")
        else:
            print("   No collections found")

    except Exception as e:
        print(f"❌ Error checking database status: {e}")

def main():
    parser = argparse.ArgumentParser(description="Initialize RBAC database system")
    parser.add_argument("--reset", action="store_true", help="Reset existing database before initialization")
    parser.add_argument("--create-users", action="store_true", help="Create default test users")
    parser.add_argument("--status", action="store_true", help="Check database status only")
    
    args = parser.parse_args()
    
    print("🚀 RBAC Database Initialization Script")
    print("=" * 50)
    
    # Check status if requested
    if args.status:
        check_database_status()
        return
    
    # Reset database if requested
    if args.reset:
        if not reset_admin_database():
            return
        print()
    
    try:
        print("🔧 Initializing RBAC system...")
        
        # Initialize the complete RBAC system
        tenant_db = initialize_complete_rbac_system()

        print("\n✅ RBAC system initialization completed successfully!")

        # Show summary
        print("\n📋 Summary:")

        # Check admin database
        admin_db = get_admin_db()
        admin_collections = admin_db.list_collection_names()
        tenant_count = admin_db.tenants.count_documents({}) if "tenants" in admin_collections else 0
        print(f"   Admin database tenants: {tenant_count}")

        # Check tenant database
        if tenant_db is not None:
            tenant_collections = tenant_db.list_collection_names()
            for collection_name in ["users", "roles", "permissions", "config"]:
                if collection_name in tenant_collections:
                    count = tenant_db[collection_name].count_documents({})
                    print(f"   Tenant {collection_name}: {count} documents")

            # Show default users created
            users = list(tenant_db.users.find({}, {"username": 1, "role": 1, "_id": 0}))
            if users:
                print("\n👥 Default users created:")
                for user in users:
                    print(f"   {user['username']} ({user['role']})")
        
        print("\n🎉 Database initialization complete!")
        print("\nNext steps:")
        print("1. Start your FastAPI application")
        print("2. Use the /v1/rbac/stats endpoint to verify the setup")
        print("3. Test login with the default users")
        print("4. Use /v1/rbac/create-dummy-user to create additional test users")
        
    except Exception as e:
        print(f"❌ Error during initialization: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
