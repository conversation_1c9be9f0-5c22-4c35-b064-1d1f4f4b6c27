"""
RBAC System Initialization Module

This module provides comprehensive Role-Based Access Control (RBAC) initialization
with four distinct roles: Admin, Supervisor, Agent, and User.
"""

from pymongo import MongoClient, AsyncMongoClient
from pymongo.database import Database
from dotenv import load_dotenv
import os
from argon2 import PasswordHasher
from typing import Dict, List, Any
import asyncio

load_dotenv()

ph = PasswordHasher()

def get_async_client():
    """Get async MongoDB client"""
    return AsyncMongoClient(os.getenv("MONGO_URI"))

def get_sync_client():
    """Get sync MongoDB client"""
    return MongoClient(os.getenv("MONGO_URI"))

# RBAC Role Hierarchy (higher number = more privileges)
ROLE_HIERARCHY = {
    "user": 1,
    "agent": 2, 
    "supervisor": 3,
    "admin": 4
}

# Comprehensive permissions for the RBAC system
RBAC_PERMISSIONS = [
    # User Management Permissions
    {"name": "create_user", "description": "Can create new users", "tags": ["user", "management"]},
    {"name": "read_user", "description": "Can view user information", "tags": ["user", "read"]},
    {"name": "update_user", "description": "Can update user information", "tags": ["user", "management"]},
    {"name": "delete_user", "description": "Can delete users", "tags": ["user", "management"]},
    {"name": "invite_user", "description": "Can invite new users", "tags": ["user", "management"]},
    {"name": "reset_user_password", "description": "Can reset user passwords", "tags": ["user", "security"]},
    {"name": "read_user_permissions", "description": "Can view user permissions", "tags": ["user", "permissions"]},
    {"name": "update_user_permissions", "description": "Can modify user permissions", "tags": ["user", "permissions"]},
    
    # Role Management Permissions
    {"name": "create_role", "description": "Can create new roles", "tags": ["roles", "management"]},
    {"name": "read_roles", "description": "Can view roles", "tags": ["roles", "read"]},
    {"name": "update_role", "description": "Can update role information", "tags": ["roles", "management"]},
    {"name": "delete_role", "description": "Can delete roles", "tags": ["roles", "management"]},
    {"name": "assign_role", "description": "Can assign roles to users", "tags": ["roles", "management"]},
    
    # Permission Management
    {"name": "create_permission", "description": "Can create new permissions", "tags": ["permissions", "management"]},
    {"name": "read_permissions", "description": "Can view all permissions", "tags": ["permissions", "read"]},
    {"name": "update_permission", "description": "Can update permissions", "tags": ["permissions", "management"]},
    {"name": "delete_permission", "description": "Can delete permissions", "tags": ["permissions", "management"]},
    
    # Configuration Management
    {"name": "create_config", "description": "Can create configuration", "tags": ["config", "management"]},
    {"name": "read_config", "description": "Can read configuration", "tags": ["config", "read"]},
    {"name": "update_config", "description": "Can update configuration", "tags": ["config", "management"]},
    {"name": "delete_config", "description": "Can delete configuration", "tags": ["config", "management"]},
    
    # System Administration
    {"name": "manage_tenants", "description": "Can manage tenant organizations", "tags": ["system", "tenants"]},
    {"name": "view_system_logs", "description": "Can view system logs", "tags": ["system", "logs"]},
    {"name": "manage_system_settings", "description": "Can manage system-wide settings", "tags": ["system", "settings"]},
    
    # Data Access Permissions
    {"name": "read_all_data", "description": "Can read all data in the system", "tags": ["data", "read"]},
    {"name": "write_data", "description": "Can create and modify data", "tags": ["data", "write"]},
    {"name": "delete_data", "description": "Can delete data", "tags": ["data", "delete"]},
    
    # Reporting and Analytics
    {"name": "view_reports", "description": "Can view reports and analytics", "tags": ["reports", "analytics"]},
    {"name": "create_reports", "description": "Can create custom reports", "tags": ["reports", "create"]},
    {"name": "export_data", "description": "Can export data", "tags": ["data", "export"]},
    
    # Agent-specific permissions
    {"name": "handle_cases", "description": "Can handle and process cases", "tags": ["agent", "cases"]},
    {"name": "update_case_status", "description": "Can update case status", "tags": ["agent", "cases"]},
    {"name": "communicate_with_clients", "description": "Can communicate with clients", "tags": ["agent", "communication"]},
]

# Role definitions with hierarchical permissions
RBAC_ROLES = [
    {
        "name": "user",
        "description": "Basic user with read-only access",
        "hierarchy_level": 1,
        "default_permissions": [
            "read_user",  # Can view their own profile
            "read_config",  # Can read basic configuration
            "view_reports",  # Can view reports
        ]
    },
    {
        "name": "agent", 
        "description": "Agent with operational access",
        "hierarchy_level": 2,
        "default_permissions": [
            # User permissions
            "read_user", "read_config", "view_reports",
            # Agent-specific permissions
            "handle_cases", "update_case_status", "communicate_with_clients",
            "write_data", "read_all_data",
        ]
    },
    {
        "name": "supervisor",
        "description": "Supervisor who can manage agents and users",
        "hierarchy_level": 3,
        "default_permissions": [
            # All agent permissions
            "read_user", "read_config", "view_reports", "handle_cases", 
            "update_case_status", "communicate_with_clients", "write_data", "read_all_data",
            # Supervisor-specific permissions
            "create_user", "update_user", "invite_user", "reset_user_password",
            "read_user_permissions", "update_user_permissions", "assign_role",
            "read_roles", "create_reports", "export_data", "delete_data",
        ]
    },
    {
        "name": "admin",
        "description": "Administrator with full system access",
        "hierarchy_level": 4,
        "default_permissions": [permission["name"] for permission in RBAC_PERMISSIONS]  # All permissions
    }
]

# Default users for each role (for testing)
DEFAULT_USERS = [
    {
        "username": "admin_user",
        "role": "admin", 
        "password": "admin123",  # Will be hashed
        "email": "<EMAIL>"
    },
    {
        "username": "supervisor_user",
        "role": "supervisor",
        "password": "supervisor123",  # Will be hashed
        "email": "<EMAIL>"
    },
    {
        "username": "agent_user", 
        "role": "agent",
        "password": "agent123",  # Will be hashed
        "email": "<EMAIL>"
    },
    {
        "username": "basic_user",
        "role": "user",
        "password": "user123",  # Will be hashed
        "email": "<EMAIL>"
    }
]

def setup_admin_database():
    """
    Create or connect to admin database - only contains tenants collection
    """
    client = get_sync_client()
    project_name = os.getenv("PROJECT_NAME", "default").lower()
    admin_db_name = f"{project_name}_admin"

    print(f"Setting up admin database: {admin_db_name}")

    # Get or create admin database
    admin_db = client[admin_db_name]

    # Admin database only needs tenants collection
    required_collections = ["tenants"]
    existing_collections = admin_db.list_collection_names()

    # Create missing collections
    for collection in required_collections:
        if collection not in existing_collections:
            admin_db.create_collection(collection)
            print(f"Created collection: {collection}")
        else:
            print(f"Collection already exists: {collection}")

    return admin_db

def setup_tenant_database(tenant_db_name: str):
    """
    Setup a tenant database with RBAC collections
    """
    client = get_sync_client()
    tenant_db = client[tenant_db_name]

    print(f"Setting up tenant database: {tenant_db_name}")

    # Required collections for tenant database
    required_collections = ["users", "permissions", "roles", "config"]
    existing_collections = tenant_db.list_collection_names()

    # Create missing collections
    for collection in required_collections:
        if collection not in existing_collections:
            tenant_db.create_collection(collection)
            print(f"Created collection: {collection}")
        else:
            print(f"Collection already exists: {collection}")

    return tenant_db

def initialize_rbac_permissions(tenant_db: Database):
    """
    Initialize all RBAC permissions in the tenant database
    """
    permissions_collection = tenant_db.permissions

    print("Initializing RBAC permissions...")
    for permission in RBAC_PERMISSIONS:
        result = permissions_collection.find_one_and_update(
            {"name": permission["name"]},
            {"$set": permission},
            upsert=True
        )
        if result is None:
            print(f"Created permission: {permission['name']}")
        else:
            print(f"Updated permission: {permission['name']}")

def initialize_rbac_roles(tenant_db: Database):
    """
    Initialize all RBAC roles in the tenant database
    """
    roles_collection = tenant_db.roles

    print("Initializing RBAC roles...")
    for role in RBAC_ROLES:
        result = roles_collection.find_one_and_update(
            {"name": role["name"]},
            {"$set": role},
            upsert=True
        )
        if result is None:
            print(f"Created role: {role['name']}")
        else:
            print(f"Updated role: {role['name']}")

def initialize_role_hierarchy_config(tenant_db: Database):
    """
    Initialize role hierarchy configuration in tenant database
    """
    config_collection = tenant_db.config

    hierarchy_config = {
        "name": "role_hierarchy",
        "description": "Defines the hierarchical levels of roles",
        "roles": ROLE_HIERARCHY,
        "created_by": "system",
        "type": "rbac_config"
    }

    result = config_collection.find_one_and_update(
        {"name": "role_hierarchy"},
        {"$set": hierarchy_config},
        upsert=True
    )

    if result is None:
        print("Created role hierarchy configuration")
    else:
        print("Updated role hierarchy configuration")

def create_default_users(tenant_db: Database):
    """
    Create default users for each role in tenant database (for testing purposes)
    """
    users_collection = tenant_db.users
    roles_collection = tenant_db.roles

    print("Creating default users...")
    for user_data in DEFAULT_USERS:
        # Get role permissions
        role_doc = roles_collection.find_one({"name": user_data["role"]})
        if not role_doc:
            print(f"Role {user_data['role']} not found, skipping user {user_data['username']}")
            continue

        # Hash password
        hashed_password = ph.hash(user_data["password"])

        user_doc = {
            "username": user_data["username"],
            "email": user_data.get("email", f"{user_data['username']}@example.com"),
            "role": user_data["role"],
            "permissions": role_doc["default_permissions"],
            "hashed_password": hashed_password,
            "is_active": True,
            "created_by": "system"
        }

        result = users_collection.find_one_and_update(
            {"username": user_data["username"]},
            {"$set": user_doc},
            upsert=True
        )

        if result is None:
            print(f"Created user: {user_data['username']} with role: {user_data['role']}")
        else:
            print(f"Updated user: {user_data['username']} with role: {user_data['role']}")

def initialize_tenant_rbac_system(tenant_db_name: str):
    """
    Initialize RBAC system for a specific tenant database
    """
    print(f"Initializing RBAC system for tenant: {tenant_db_name}")

    # Setup tenant database
    tenant_db = setup_tenant_database(tenant_db_name)

    # Initialize permissions
    initialize_rbac_permissions(tenant_db)

    # Initialize roles
    initialize_rbac_roles(tenant_db)

    # Initialize role hierarchy configuration
    initialize_role_hierarchy_config(tenant_db)

    # Create default users
    create_default_users(tenant_db)

    print(f"RBAC system initialization completed for tenant: {tenant_db_name}")
    return tenant_db

def create_tenant_with_rbac(tenant_name: str, slug: str = None):
    """
    Create a new tenant with complete RBAC system
    """
    if slug is None:
        slug = tenant_name.lower().replace(" ", "_")

    project_name = os.getenv("PROJECT_NAME", "default").lower()
    tenant_db_name = f"{slug}_{project_name}_db"

    print(f"Creating tenant: {tenant_name} with database: {tenant_db_name}")

    # Setup admin database
    admin_db = setup_admin_database()

    # Check if tenant already exists
    existing_tenant = admin_db.tenants.find_one({"slug": slug})
    if existing_tenant:
        print(f"Tenant with slug '{slug}' already exists")
        return existing_tenant, None

    # Create tenant record in admin database
    tenant_doc = {
        "name": tenant_name,
        "slug": slug,
        "db_name": tenant_db_name,
        "created_by": "system",
        "is_active": True
    }

    result = admin_db.tenants.insert_one(tenant_doc)
    tenant_doc["_id"] = result.inserted_id

    print(f"Created tenant record: {tenant_name}")

    # Initialize RBAC system for the tenant
    tenant_db = initialize_tenant_rbac_system(tenant_db_name)

    return tenant_doc, tenant_db

def initialize_complete_rbac_system():
    """
    Complete RBAC system initialization - creates a default tenant for testing
    """
    print("Starting complete RBAC system initialization...")

    # Create default tenant for testing
    default_tenant_name = "Default Organization"
    default_slug = "default"

    tenant_doc, tenant_db = create_tenant_with_rbac(default_tenant_name, default_slug)

    print("RBAC system initialization completed successfully!")
    return tenant_db

if __name__ == "__main__":
    initialize_complete_rbac_system()
