# src/core/security.py

from datetime import datetime, timedelta
from argon2 import Pass<PERSON><PERSON>asher
from argon2.exceptions import VerifyMismatchError
from fastapi.security import OAuth2<PERSON>asswordBearer
from fastapi import Depends, HTTPException
from bson import ObjectId
import jwt
from app.core.config import SECRET_KEY, ALGORITHM
from app.models.user import UserTenantDB, User
from app.models.permission import Permission
from app.models.role import Role
from app.core.database import get_async_db_from_tenant_id, get_async_admin_db
from typing import Optional, List


ph = PasswordHasher()
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="login")

def create_access_token(data: dict, expires_delta:timedelta = None):
    to_encode = data.copy() 
    if expires_delta:   
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=1)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


async def get_tenant_info(token: str = Depends(oauth2_scheme)) -> UserTenantDB:
    credentials_exception = HTTPException(
        status_code=401,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        # print("payload: ", payload)
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except jwt.PyJWTError:
        raise credentials_exception
    
    tenant_db = await get_async_db_from_tenant_id(payload.get("tenant_id"))
    user = await tenant_db.users.find_one({"username": username})
    if user is None:
        raise credentials_exception

    # Get user permissions asynchronously
    user_permissions = []
    for permission_name in user["permissions"]:
        permission_doc = await tenant_db.permissions.find_one({"name": permission_name})
        if permission_doc:
            user_permissions.append(Permission(**permission_doc))
    user["permissions"] = user_permissions

    # Get user role asynchronously
    user_role_doc = await tenant_db.roles.find_one({"name": user["role"]})
    if user_role_doc:
        user_role = Role(**user_role_doc)
        user["role"] = user_role
    
    return UserTenantDB(tenant_id=payload.get("tenant_id"), db=tenant_db, user=User(**user))


def min_role(min_role: str):
    async def check_role(user_tenant_info: UserTenantDB = Depends(get_tenant_info)):
        user_role = user_tenant_info.user.role.name
        # Get role hierarchy from config collection
        hierarchy_config = await user_tenant_info.db.config.find_one({"name": "role_hierarchy"})
        if not hierarchy_config:
            raise HTTPException(status_code=500, detail="Role hierarchy configuration not found")

        ROLES = hierarchy_config['roles']
        if ROLES.get(user_role, 0) < ROLES.get(min_role, 0):
            raise HTTPException(status_code=403, detail="You are not authorized to carry out this action")
        return user_tenant_info
    return check_role

def create_invitation_token(username: str, role: str, invited_by: str, tenant_id:str, expires_delta: Optional[timedelta] = None):
    """
    Creates a JWT token for agent invitation.
    """
    to_encode = {"username": username, "invited_by": invited_by, "role": role, "tenant_id": tenant_id}
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=7)  # Default expiration: 7 days
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_invitation_token(token: str):
    """
    Verifies the invitation token and extracts the agent's name.
    """
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("username")
        invited_by: str = payload.get("invited_by")
        role: str = payload.get("role")
        result = get_db_from_tenant_id(payload.get("tenant_id")).invitations.find_one({"username": username, "role": role})

        if username is None or invited_by is None:
            raise HTTPException(status_code=400, detail="Invalid invitation token")

        if result is None:
            raise HTTPException(status_code=400, detail="Invalid invitation token")
        
        return username, invited_by, role
    
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=400, detail="Invitation token has expired")
    except jwt.PyJWTError:
        raise HTTPException(status_code=400, detail="Invalid invitation token")


# Replace CryptContext with PasswordHasher
def hash_password(password: str) -> str:
    """
    Hash new passwords using Argon2
    """
    return ph.hash(password)
    
# Update the verify_password function
def verify_password(plain_password, hashed_password):
    """
    Verifies a plain password against a hashed password.
    """
    try:
        return ph.verify(hashed_password, plain_password)
    except VerifyMismatchError:
        return False


def require_permissions(required_permissions: list[str]):
    """
    Dependency that checks if the user has all the required permissions.
    Usage: @router.get("/endpoint", dependencies=[Depends(require_permissions(["read:users", "write:users"]))])
    """
    async def check_permissions(user_tenant_info: UserTenantDB = Depends(get_tenant_info)):
        user_permissions = [p.name for p in user_tenant_info.user.permissions]
        
        missing_permissions = [perm for perm in required_permissions if perm not in user_permissions]
        
        if missing_permissions:
            raise HTTPException(
                status_code=403,
                detail=f"Missing required permissions: {', '.join(missing_permissions)}"
            )
        
        return user_tenant_info
    return check_permissions


def check_user_has_permission(user: User, permission_name: str) -> bool:
    """
    Check if a user has a specific permission
    """
    user_permissions = [permission.name for permission in user.permissions]
    return permission_name in user_permissions

def check_role_hierarchy(user_role: str, required_role: str, hierarchy_config: dict) -> bool:
    """
    Check if user role meets the minimum required role level
    """
    user_level = hierarchy_config.get(user_role, 0)
    required_level = hierarchy_config.get(required_role, 0)
    return user_level >= required_level

async def get_user_by_username(username: str, tenant_db) -> Optional[dict]:
    """
    Get user by username from tenant database
    """
    return await tenant_db.users.find_one({"username": username})

async def create_user_with_role(user_data: dict, tenant_db) -> str:
    """
    Create a new user with specified role and permissions
    """
    # Get role permissions
    role_doc = await tenant_db.roles.find_one({"name": user_data["role"]})
    if not role_doc:
        raise HTTPException(status_code=400, detail=f"Role {user_data['role']} not found")

    # Hash password
    hashed_password = ph.hash(user_data["password"])

    user_doc = {
        "username": user_data["username"],
        "email": user_data.get("email", f"{user_data['username']}@example.com"),
        "role": user_data["role"],
        "permissions": role_doc["default_permissions"],
        "hashed_password": hashed_password,
        "is_active": True,
        "created_by": user_data.get("created_by", "system")
    }

    result = await tenant_db.users.insert_one(user_doc)
    return str(result.inserted_id)
