from pymongo import MongoClient, AsyncMongoClient
from dotenv import load_dotenv
from bson import ObjectId
import os
from fastapi import HTTPException
load_dotenv()

# Global async client instance
_async_client = None

def get_async_client():
    """Get or create async MongoDB client"""
    global _async_client
    if _async_client is None:
        _async_client = AsyncMongoClient(os.getenv("MONGO_URI"))
    return _async_client

def get_admin_db():
    """Synchronous admin database connection - deprecated, use get_async_admin_db instead"""
    ADMIN_DB_NAME = os.getenv("PROJECT_NAME").lower() + "_admin"
    try:
        return MongoClient(os.getenv("MONGO_URI"))[ADMIN_DB_NAME]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Admin database not found\nError:{e}")

def get_async_admin_db():
    """Get async admin database connection"""
    ADMIN_DB_NAME = os.getenv("PROJECT_NAME").lower() + "_admin"
    try:
        client = get_async_client()
        return client[ADMIN_DB_NAME]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Admin database not found\nError:{e}")

async def get_async_db_from_tenant_id(tenant_id: str):
    """Get async tenant database connection from tenant ID"""
    try:
        admin_db = get_async_admin_db()
        tenant_doc = await admin_db.tenants.find_one({"_id": ObjectId(tenant_id)})
        if not tenant_doc:
            raise Exception("Tenant not found")

        tenant_database_name = tenant_doc["db_name"]
        client = get_async_client()
        return client[tenant_database_name]
    except Exception as e:
        raise HTTPException(status_code=401, detail=f"Invalid tenant credentials\nError:{e}")

def get_db_from_tenant_id(tenant_id: str):
    """Synchronous tenant database connection - deprecated, use get_async_db_from_tenant_id instead"""
    try:
        tenant_database_name = get_admin_db().tenants.find_one({"_id": ObjectId(tenant_id)})["db_name"]
        return MongoClient(os.getenv("MONGO_URI"))[tenant_database_name]
    except Exception as e:
        raise HTTPException(status_code=401, detail=f"Invalid tenant credentials\nError:{e}")

async def get_async_tenant_id_and_name_from_slug(slug: str):
    """Get tenant ID and name from slug asynchronously"""
    try:
        admin_db = get_async_admin_db()
        tenant_id_and_name = await admin_db.tenants.find_one({"slug": slug}, {"_id": 1, "name": 1})
        if not tenant_id_and_name:
            raise Exception("Slug not found")
        return tenant_id_and_name
    except Exception as e:
        raise HTTPException(status_code=401, detail=f"Invalid slug credentials\nError:{e}")

def get_tenant_id_and_name_from_slug(slug: str):
    """Synchronous tenant lookup - deprecated, use get_async_tenant_id_and_name_from_slug instead"""
    try:
        tenant_id_and_name = get_admin_db().tenants.find_one({"slug":slug},{"_id": 1, "name": 1})
        if not tenant_id_and_name:
            raise Exception("Slug not found")
        return tenant_id_and_name
    except Exception as e:
        print("raised an error")
        raise HTTPException(status_code=401, detail=f"Invalid slug credentials\nError{e}")

async def get_async_db_from_tenant_slug(slug: str):
    """Get async tenant database connection from slug"""
    try:
        admin_db = get_async_admin_db()
        tenant_doc = await admin_db.tenants.find_one({"slug": slug})
        if not tenant_doc:
            raise Exception("Tenant not found")

        tenant_database_name = tenant_doc["db_name"]
        client = get_async_client()
        return client[tenant_database_name]
    except Exception as e:
        raise HTTPException(status_code=401, detail=f"Invalid slug credentials\nError:{e}")

def get_sync_db_from_tenant_slug(slug: str):
    """Synchronous tenant database from slug - deprecated, use async version instead"""
    try:
        tenant_database_name = get_admin_db().tenants.find_one({"slug":slug})["db_name"]
        return MongoClient(os.getenv("MONGO_URI"))[tenant_database_name]
    except Exception as e:
        raise HTTPException(status_code=401, detail=f"Invalid slug credentials\nError{e}")
