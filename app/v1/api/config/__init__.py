from fastapi import APIRouter, Depends
from bson import ObjectId

from app.core.helper import logger
from app.core.security import get_tenant_info, require_permissions
from app.models.user import UserTenantDB

loggers = logger.setup_new_logging(__name__)

router = APIRouter(tags=["Config"])

@router.get("/configs")
async def get_all_configs(
    current_user: UserTenantDB = Depends(require_permissions(["read_config"]))
):
    """Get all configurations from the config collection"""
    try:
        configs_collection = current_user.db.config
        configs = list(configs_collection.find({}))
        
        # Convert ObjectId to string for JSON serialization
        for config in configs:
            config["_id"] = str(config["_id"])
        
        return {"configs": configs}
    except Exception as e:
        loggers.error(f"Error fetching configs: {str(e)}")
        raise HTTPException(status_code=500, detail="Error fetching configurations")