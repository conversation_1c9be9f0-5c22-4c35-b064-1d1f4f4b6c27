"""
RBAC Management API Endpoints

This module provides API endpoints for managing the Role-Based Access Control system,
including creating dummy users for testing and managing roles/permissions.
"""

from fastapi import APIRouter, HTTPException, Depends, status
from typing import List, Dict, Any
from pydantic import BaseModel, Field
from bson import ObjectId

from app.core.security import (
    get_tenant_info, 
    require_permissions, 
    create_user_with_role,
    ph
)
from app.core.database import get_async_admin_db
from app.core.rbac_init import (
    RBAC_ROLES, 
    DEFAULT_USERS, 
    RBAC_PERMISSIONS,
    ROLE_HIERARCHY
)
from app.models.user import UserTenantDB

router = APIRouter(tags=["RBAC Management"])

# Pydantic models for API requests/responses
class DummyUserRequest(BaseModel):
    role: str = Field(..., description="Role to assign to the dummy user")
    username_prefix: str = Field(default="test", description="Prefix for the username")
    password: str = Field(default="password123", description="Password for the dummy user")

class DummyUserResponse(BaseModel):
    user_id: str
    username: str
    role: str
    permissions: List[str]
    message: str

class RoleInfo(BaseModel):
    name: str
    description: str
    hierarchy_level: int
    permissions_count: int
    default_permissions: List[str]

class SystemStatsResponse(BaseModel):
    total_roles: int
    total_permissions: int
    role_hierarchy: Dict[str, int]
    roles: List[RoleInfo]

@router.get("/rbac/stats", response_model=SystemStatsResponse)
async def get_rbac_system_stats(
    current_user: UserTenantDB = Depends(require_permissions(["read_config", "read_roles"]))
):
    """
    Get comprehensive statistics about the RBAC system
    """
    try:
        # Get roles and permissions from tenant database
        roles_cursor = current_user.db.roles.find({})
        roles = await roles_cursor.to_list(length=None)
        
        permissions_cursor = current_user.db.permissions.find({})
        permissions = await permissions_cursor.to_list(length=None)
        
        # Get role hierarchy config
        hierarchy_config = await current_user.db.config.find_one({"name": "role_hierarchy"})
        role_hierarchy = hierarchy_config.get("roles", {}) if hierarchy_config else ROLE_HIERARCHY
        
        # Format role information
        role_info = []
        for role in roles:
            role_info.append(RoleInfo(
                name=role["name"],
                description=role.get("description", ""),
                hierarchy_level=role.get("hierarchy_level", 0),
                permissions_count=len(role.get("default_permissions", [])),
                default_permissions=role.get("default_permissions", [])
            ))
        
        return SystemStatsResponse(
            total_roles=len(roles),
            total_permissions=len(permissions),
            role_hierarchy=role_hierarchy,
            roles=role_info
        )
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving RBAC stats: {str(e)}"
        )

@router.post("/rbac/create-dummy-user", response_model=DummyUserResponse)
async def create_dummy_user(
    request: DummyUserRequest,
    current_user: UserTenantDB = Depends(require_permissions(["create_user"]))
):
    """
    Create a dummy user for testing purposes with the specified role
    """
    try:
        # Validate role exists
        role_doc = await current_user.db.roles.find_one({"name": request.role})
        if not role_doc:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Role '{request.role}' does not exist"
            )
        
        # Generate unique username
        base_username = f"{request.username_prefix}_{request.role}"
        counter = 1
        username = base_username
        
        while await current_user.db.users.find_one({"username": username}):
            username = f"{base_username}_{counter}"
            counter += 1
        
        # Create user data
        user_data = {
            "username": username,
            "password": request.password,
            "role": request.role,
            "email": f"{username}@example.com",
            "created_by": current_user.user.username
        }
        
        # Create the user
        user_id = await create_user_with_role(user_data, current_user.db)
        
        return DummyUserResponse(
            user_id=user_id,
            username=username,
            role=request.role,
            permissions=role_doc["default_permissions"],
            message=f"Dummy user '{username}' created successfully with role '{request.role}'"
        )
    
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating dummy user: {str(e)}"
        )

@router.post("/rbac/create-all-dummy-users")
async def create_all_dummy_users(
    current_user: UserTenantDB = Depends(require_permissions(["create_user"]))
):
    """
    Create dummy users for all available roles
    """
    try:
        created_users = []
        
        # Get all roles from database
        roles_cursor = current_user.db.roles.find({})
        roles = await roles_cursor.to_list(length=None)
        
        for role in roles:
            role_name = role["name"]
            
            # Generate unique username
            base_username = f"dummy_{role_name}"
            counter = 1
            username = base_username
            
            while await current_user.db.users.find_one({"username": username}):
                username = f"{base_username}_{counter}"
                counter += 1
            
            # Create user data
            user_data = {
                "username": username,
                "password": f"{role_name}123",  # Role-based password
                "role": role_name,
                "email": f"{username}@example.com",
                "created_by": current_user.user.username
            }
            
            # Create the user
            user_id = await create_user_with_role(user_data, current_user.db)
            
            created_users.append({
                "user_id": user_id,
                "username": username,
                "role": role_name,
                "password": user_data["password"],
                "permissions": role["default_permissions"]
            })
        
        return {
            "message": f"Successfully created {len(created_users)} dummy users",
            "users": created_users
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating dummy users: {str(e)}"
        )

@router.get("/rbac/roles")
async def get_all_roles(
    current_user: UserTenantDB = Depends(require_permissions(["read_roles"]))
):
    """
    Get all available roles in the system
    """
    try:
        roles_cursor = current_user.db.roles.find({})
        roles = await roles_cursor.to_list(length=None)
        
        # Convert ObjectId to string for JSON serialization
        for role in roles:
            role["_id"] = str(role["_id"])
        
        return {"roles": roles}
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving roles: {str(e)}"
        )

@router.get("/rbac/permissions")
async def get_all_permissions(
    current_user: UserTenantDB = Depends(require_permissions(["read_permissions"]))
):
    """
    Get all available permissions in the system
    """
    try:
        permissions_cursor = current_user.db.permissions.find({})
        permissions = await permissions_cursor.to_list(length=None)
        
        # Convert ObjectId to string for JSON serialization
        for permission in permissions:
            permission["_id"] = str(permission["_id"])
        
        return {"permissions": permissions}
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving permissions: {str(e)}"
        )

@router.post("/rbac/initialize-system")
async def initialize_rbac_system(
    current_user: UserTenantDB = Depends(require_permissions(["manage_system_settings"]))
):
    """
    Initialize or reinitialize the RBAC system with default roles and permissions
    """
    try:
        from app.core.rbac_init import (
            initialize_rbac_permissions,
            initialize_rbac_roles, 
            initialize_role_hierarchy_config
        )
        
        # Initialize permissions
        await initialize_rbac_permissions_async(current_user.db)
        
        # Initialize roles  
        await initialize_rbac_roles_async(current_user.db)
        
        # Initialize role hierarchy
        await initialize_role_hierarchy_config_async(current_user.db)
        
        return {
            "message": "RBAC system initialized successfully",
            "permissions_count": len(RBAC_PERMISSIONS),
            "roles_count": len(RBAC_ROLES)
        }
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error initializing RBAC system: {str(e)}"
        )

# Async versions of initialization functions
async def initialize_rbac_permissions_async(db):
    """Async version of permission initialization"""
    for permission in RBAC_PERMISSIONS:
        await db.permissions.find_one_and_update(
            {"name": permission["name"]},
            {"$set": permission},
            upsert=True
        )

async def initialize_rbac_roles_async(db):
    """Async version of role initialization"""
    for role in RBAC_ROLES:
        await db.roles.find_one_and_update(
            {"name": role["name"]},
            {"$set": role},
            upsert=True
        )

async def initialize_role_hierarchy_config_async(db):
    """Async version of role hierarchy initialization"""
    hierarchy_config = {
        "name": "role_hierarchy",
        "description": "Defines the hierarchical levels of roles",
        "roles": ROLE_HIERARCHY,
        "created_by": "system",
        "type": "rbac_config"
    }
    
    await db.config.find_one_and_update(
        {"name": "role_hierarchy"},
        {"$set": hierarchy_config},
        upsert=True
    )
