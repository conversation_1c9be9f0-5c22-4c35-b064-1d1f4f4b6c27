from fastapi import APIRout<PERSON>, HTTPException, Depends
from bson import ObjectId

from app.core.config import SECRET_KEY, ALGORITHM
from app.core.helper import logger
from app.core.security import  get_tenant_info, require_permissions
from app.core.database import get_db_from_tenant_id, get_tenant_id_and_name_from_slug
from app.models.security import OAuth2PasswordRequestFormWithClientID, ChangePasswordRequest, ResetPasswordRequest
from app.core.security import (
    create_access_token, 
    verify_password, 
    create_invitation_token,
    verify_invitation_token,
    get_tenant_info,
    min_role,
    ph  # Replace pwd_context with ph
)
from app.models.user import AgentInvitation, AgentRegistration
from app.core.helper.mongo_helper import convert_objectid_to_str
from datetime import timedelta
from app.models.user import UserTenantDB
from datetime import datetime, timedelta
from app.v1.api.users.user_permissions import router as permissions_router
loggers = logger.setup_new_logging(__name__)

router = APIRouter(tags=["Users"])

# Include the permissions router
router.include_router(permissions_router)


@router.get("/get_tenant_id")
async def tenantid_from_slug(slug: str):
    try:
        result = get_tenant_id_and_name_from_slug(slug)
        tenant_id = str(result["_id"])
        tenant_name = result["name"]
        
        return{
            "tenant_id":tenant_id,
            "tenant_name":tenant_name
        }
    except Exception:
        raise Exception

@router.post("/login")
async def login(form_data: OAuth2PasswordRequestFormWithClientID = Depends()):    
    # find the database name of that tenant
    result = get_tenant_id_and_name_from_slug(form_data.client_id)
    
    tenant_id = str(result["_id"])
    tenant_database = get_db_from_tenant_id(tenant_id)
    # print(tenant_database)
    # connect to the user_collection of that database. 
    user = tenant_database.users.find_one({"username": form_data.username})
    
    if not user :
        loggers.error(f"User not found: {form_data.username}")
        raise HTTPException(status_code=401, detail="User not found")

    if not verify_password(form_data.password, user["hashed_password"]):
        loggers.error(f"Incorrect Credentials: {form_data.username}")
        raise HTTPException(status_code=401, detail="Incorrect Credentials")
    
    access_token = create_access_token(
        data={"sub": user["username"], "role": user["role"], "tenant_id": tenant_id},
        expires_delta=timedelta(days=1)
    )
 

    user = convert_objectid_to_str(user)

    # print(result)
    loggers.info(
        msg=f"Tenant Database: {tenant_database}\tUser: {user}"
    )
    
    return {
        "id": user["_id"],
        "access_token": access_token,
        "token_type": "bearer",
        "username": user['username'],
        "role": user['role'],
        "tenant_id": tenant_id,
        "tenant_label": result["name"],
        "tenant_slug": form_data.client_id,
    }





@router.get("/verify_token")
async def verify_token(current_user: UserTenantDB = Depends(get_tenant_info)):
    """If the token is valid, return the user's details
       If the token is invalid, get_current_user will raise an exception
    """
    return True


@router.post("/users/invite")
async def invite_agent(
    invitation: AgentInvitation,
    current_user: UserTenantDB = Depends(require_permissions(["invite_user"]))
    ):
    """
    Invite a new agent by generating a registration link with a token.
    Only admins and supervisors can invite agents.
    """

    # invited_by = "god"
    tenant_id = current_user.tenant_id
    users_collection = current_user.db.users
    tenant_role = current_user.user.role
    # if tenant_role not in ["admin", "supervisor"]:
    #     raise HTTPException(status_code=403, detail="Not authorized to invite agents")

    invited_by = str(tenant_id)
    print(invited_by)

    existing_user = users_collection.find_one({"username": invitation.username})
    if existing_user:
        return {"registration_token": None, "success": False, "msg": "Username already exists"}
    
    invitations_collection = current_user.db.invitations
    existing_invitation = invitations_collection.find_one({"username": invitation.username})
    if existing_invitation:
        try:
            result = invitations_collection.delete_many({"username": invitation.username})
            if result.deleted_count > 0:
                loggers.info(f"Record with username '{invitation.username}' existed, therefore deleted existing invitation to create new one.")
        except HTTPException as e:
            loggers.error(e)
            raise e

    # # Create invitation token
    token = create_invitation_token(username=invitation.username,
                                    role=invitation.role, 
                                    invited_by=invited_by,
                                    tenant_id=tenant_id,
                                    expires_delta=timedelta(days=7))  # Valid for 7 days
    
    invitation_record = {
        "username": invitation.username,
        "token": token,
        "role": invitation.role,
        "invited_by": invited_by,
        "expires_at": datetime.now() + timedelta(days=7),
        "used": False,
        # "permissions":invitation.permissions
    }

    invitations_collection.insert_one(invitation_record)
    
    return {"registration_token": token, "success": True, "msg": "Token Generated!"}


@router.post("/users/register")
async def register_agent(registration: AgentRegistration):
    """
    Register a new agent using the invitation token.
    """
    # Verify the token and extract the agent's name
    try:
        agent_username, invited_by, role_ = verify_invitation_token(registration.token)
    except HTTPException as e:
        loggers.error(e)
        raise e
    
    tenant_database =  get_db_from_tenant_id(invited_by)
    invitations_collection = tenant_database.invitations
    invitation = invitations_collection.find_one({"token": registration.token})

    if not invitation:
        return {"msg": "Invalid invitation token!", "success": False}
    if invitation.get("used"):
        return {"msg": "Invitation token has already been used. Request the supervisor to generate a new one!", "success": False}
    
    users_collection = tenant_database.users
    # Check if the desired username already exists
    existing_user = users_collection.find_one({"username": agent_username})
    if existing_user:
        return {"msg": "Username already exists!", "success": False}
    
    # Hash the provided password
    hashed_password = ph.hash(registration.password)

    permissions = tenant_database.roles.find_one({"name":role_}).get("default_permissions")
    
    # Create the new agent user
    new_agent = {
        "username": registration.username,
        "hashed_password": hashed_password,
        "role": role_,
        "created_by": invited_by,
        "created_at": datetime.now(),
        "permissions":permissions
    }
    
    # Insert the new agent into the database
    result = users_collection.insert_one(new_agent)
    new_agent["_id"] = result.inserted_id

    invitations_collection.update_one(
        {"token": registration.token},
        {"$set": {"used": True}}
    )
    
    return {"msg": "Agent registered successfully", "success": True}



@router.post("/users/change_password")
async def change_password(req: ChangePasswordRequest,
                          user_tenant_info: UserTenantDB = Depends(get_tenant_info)):
    
    users_collection = user_tenant_info.db.users
    
    user = users_collection.find_one({"_id": ObjectId(user_tenant_info.user.id)}) 
    if not user:
        raise HTTPException(
            status_code=404, 
            detail="User not found."
        )
    
    if not verify_password(req.old_password, user["hashed_password"]):
        raise HTTPException(
            status_code=400,
            detail="Current password is incorrect."
        )
    
    if verify_password(req.new_password, user["hashed_password"]):
        raise HTTPException(
            status_code=400,
            detail="New password must be different from current password"
        )
    
    new_hashed_password = ph.hash(req.new_password)
    result = users_collection.update_one(
        {"username": user["username"]},
        {"$set": {"hashed_password": new_hashed_password}}
    )
    
    if result.modified_count == 0:
        raise HTTPException(
            status_code=500,
            detail="Failed to update password."
        )
    
    loggers.info(f"Password successfully changed for user: {user['username']}")
    return {"message": "Password successfully changed."}

@router.post("/users/reset_password")
async def reset_password(
    req: ResetPasswordRequest, 
    user_tenant_info: UserTenantDB = Depends(require_permissions(["reset_user_password"]))
):
    users_collection = user_tenant_info.db.users
    
    user = users_collection.find_one({"username": user_tenant_info.user.username})
    if not user:
        raise HTTPException(status_code=404, detail="User not found.")
    
    new_random_password = ''.join(secrets.choice('0123456789abcdef') for _ in range(10))
    new_hashed_password = ph.hash(new_random_password)
    
    if req.subordinate_id:
        subordinate = users_collection.find_one({"_id": ObjectId(req.subordinate_id)})
        if not subordinate:
            raise HTTPException(status_code=404, 
                                detail="Subordinate user doesn't exist.")
        
        result = users_collection.update_one(
            {"_id": ObjectId(req.subordinate_id)},
            {"$set": {"hashed_password": new_hashed_password}}
        )
        
        loggers.info(f"Subordinate Password reset successfully by user: {user['username']}")
        return {"message": f"Subordinate Password reset to {new_random_password} successfully."}
    
    result = users_collection.update_one(
        {"_id": ObjectId(user["_id"])},
        {"$set": {"hashed_password": new_hashed_password}}
    )
    
    if result.modified_count == 0:
        raise HTTPException(
            status_code=500,
            detail="Failed to reset password."
        )
    
    loggers.info(f"Password reset successfully by user: {user['username']}")
    return {"message": f"Password reset to {new_random_password} successfully."}


# Remove the reset_permissions endpoint from here