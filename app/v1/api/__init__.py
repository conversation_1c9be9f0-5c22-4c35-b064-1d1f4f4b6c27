from fastapi import FastAPI

from .users import router as user_router
from .config import router as config_router
from .roles import router as role_router
from .permissions import router as permissions_router
from .rbac import router as rbac_router


router = FastAPI(title="MyApp API v1")

router.include_router(user_router)
router.include_router(config_router)
router.include_router(role_router)
router.include_router(permissions_router)
router.include_router(rbac_router)
