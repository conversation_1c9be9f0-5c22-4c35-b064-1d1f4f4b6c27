"""
Comprehensive tests for the RBAC (Role-Based Access Control) system

Tests cover:
- Role hierarchy validation
- Permission checking
- User creation with roles
- API endpoint security
- Database initialization
"""

import pytest
import asyncio
from fastapi.testclient import TestClient
from unittest.mock import AsyncMock, MagicMock
from bson import ObjectId

from app.core.rbac_init import (
    RBAC_ROLES, 
    RBAC_PERMISSIONS, 
    ROLE_HIERARCHY,
    initialize_complete_rbac_system
)
from app.core.security import (
    check_user_has_permission,
    check_role_hierarchy,
    create_user_with_role,
    ph
)
from app.models.user import User
from app.models.permission import Permission
from app.models.role import Role

class TestRoleHierarchy:
    """Test role hierarchy functionality"""
    
    def test_role_hierarchy_levels(self):
        """Test that role hierarchy levels are correctly defined"""
        assert ROLE_HIERARCHY["user"] == 1
        assert ROLE_HIERARCHY["agent"] == 2
        assert ROLE_HIERARCHY["supervisor"] == 3
        assert ROLE_HIERARCHY["admin"] == 4
    
    def test_check_role_hierarchy_valid(self):
        """Test role hierarchy checking with valid roles"""
        # Admin should have access to all lower roles
        assert check_role_hierarchy("admin", "user", R<PERSON><PERSON>_HIERARCHY) == True
        assert check_role_hierarchy("admin", "agent", ROLE_HIERARCHY) == True
        assert check_role_hierarchy("admin", "supervisor", ROLE_HIERARCHY) == True
        assert check_role_hierarchy("admin", "admin", ROLE_HIERARCHY) == True
        
        # Supervisor should have access to agent and user
        assert check_role_hierarchy("supervisor", "user", ROLE_HIERARCHY) == True
        assert check_role_hierarchy("supervisor", "agent", ROLE_HIERARCHY) == True
        assert check_role_hierarchy("supervisor", "supervisor", ROLE_HIERARCHY) == True
        
        # Agent should have access to user
        assert check_role_hierarchy("agent", "user", ROLE_HIERARCHY) == True
        assert check_role_hierarchy("agent", "agent", ROLE_HIERARCHY) == True
    
    def test_check_role_hierarchy_invalid(self):
        """Test role hierarchy checking with invalid access"""
        # User should not have access to higher roles
        assert check_role_hierarchy("user", "agent", ROLE_HIERARCHY) == False
        assert check_role_hierarchy("user", "supervisor", ROLE_HIERARCHY) == False
        assert check_role_hierarchy("user", "admin", ROLE_HIERARCHY) == False
        
        # Agent should not have access to supervisor or admin
        assert check_role_hierarchy("agent", "supervisor", ROLE_HIERARCHY) == False
        assert check_role_hierarchy("agent", "admin", ROLE_HIERARCHY) == False
        
        # Supervisor should not have access to admin
        assert check_role_hierarchy("supervisor", "admin", ROLE_HIERARCHY) == False

class TestPermissions:
    """Test permission functionality"""
    
    def test_rbac_permissions_structure(self):
        """Test that RBAC permissions are properly structured"""
        assert len(RBAC_PERMISSIONS) > 0
        
        for permission in RBAC_PERMISSIONS:
            assert "name" in permission
            assert "description" in permission
            assert "tags" in permission
            assert isinstance(permission["tags"], list)
    
    def test_rbac_roles_structure(self):
        """Test that RBAC roles are properly structured"""
        assert len(RBAC_ROLES) == 4  # user, agent, supervisor, admin
        
        for role in RBAC_ROLES:
            assert "name" in role
            assert "description" in role
            assert "hierarchy_level" in role
            assert "default_permissions" in role
            assert isinstance(role["default_permissions"], list)
    
    def test_role_permission_hierarchy(self):
        """Test that higher roles have more permissions than lower roles"""
        role_permissions = {}
        for role in RBAC_ROLES:
            role_permissions[role["name"]] = set(role["default_permissions"])
        
        # User should have the fewest permissions
        user_perms = role_permissions["user"]
        agent_perms = role_permissions["agent"]
        supervisor_perms = role_permissions["supervisor"]
        admin_perms = role_permissions["admin"]
        
        # Check that higher roles include lower role permissions
        assert user_perms.issubset(agent_perms)
        assert agent_perms.issubset(supervisor_perms)
        assert supervisor_perms.issubset(admin_perms)
        
        # Admin should have all permissions
        all_permission_names = {p["name"] for p in RBAC_PERMISSIONS}
        assert admin_perms == all_permission_names

class TestUserPermissionChecking:
    """Test user permission checking functionality"""
    
    def create_mock_user(self, role_name: str, permissions: list) -> User:
        """Create a mock user for testing"""
        mock_permissions = [
            Permission(_id=ObjectId(), name=perm, description=f"Test {perm}", tags=["test"])
            for perm in permissions
        ]
        
        mock_role = Role(
            _id=ObjectId(),
            name=role_name,
            default_permissions=permissions
        )
        
        return User(
            _id=ObjectId(),
            username=f"test_{role_name}",
            role=mock_role,
            permissions=mock_permissions
        )
    
    def test_check_user_has_permission_valid(self):
        """Test checking valid user permissions"""
        user = self.create_mock_user("agent", ["read_user", "write_data", "handle_cases"])
        
        assert check_user_has_permission(user, "read_user") == True
        assert check_user_has_permission(user, "write_data") == True
        assert check_user_has_permission(user, "handle_cases") == True
    
    def test_check_user_has_permission_invalid(self):
        """Test checking invalid user permissions"""
        user = self.create_mock_user("user", ["read_user", "read_config"])
        
        assert check_user_has_permission(user, "create_user") == False
        assert check_user_has_permission(user, "delete_user") == False
        assert check_user_has_permission(user, "manage_tenants") == False

class TestAsyncUserCreation:
    """Test async user creation functionality"""
    
    @pytest.mark.asyncio
    async def test_create_user_with_role_success(self):
        """Test successful user creation with role"""
        # Mock database
        mock_db = MagicMock()
        mock_db.roles.find_one = AsyncMock(return_value={
            "name": "agent",
            "default_permissions": ["read_user", "write_data", "handle_cases"]
        })
        mock_db.users.insert_one = AsyncMock(return_value=MagicMock(inserted_id=ObjectId()))
        
        user_data = {
            "username": "test_agent",
            "password": "test123",
            "role": "agent",
            "email": "<EMAIL>"
        }
        
        user_id = await create_user_with_role(user_data, mock_db)
        
        assert user_id is not None
        mock_db.roles.find_one.assert_called_once_with({"name": "agent"})
        mock_db.users.insert_one.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_user_with_invalid_role(self):
        """Test user creation with invalid role"""
        # Mock database
        mock_db = MagicMock()
        mock_db.roles.find_one = AsyncMock(return_value=None)
        
        user_data = {
            "username": "test_user",
            "password": "test123",
            "role": "invalid_role",
            "email": "<EMAIL>"
        }
        
        with pytest.raises(Exception):  # Should raise HTTPException
            await create_user_with_role(user_data, mock_db)

class TestPasswordHashing:
    """Test password hashing functionality"""
    
    def test_password_hashing(self):
        """Test that passwords are properly hashed"""
        password = "test123"
        hashed = ph.hash(password)
        
        assert hashed != password
        assert ph.verify(hashed, password) == True
    
    def test_password_verification_invalid(self):
        """Test password verification with wrong password"""
        password = "test123"
        wrong_password = "wrong123"
        hashed = ph.hash(password)
        
        assert ph.verify(hashed, wrong_password) == False

class TestRBACInitialization:
    """Test RBAC system initialization"""
    
    def test_rbac_roles_count(self):
        """Test that all required roles are defined"""
        role_names = {role["name"] for role in RBAC_ROLES}
        expected_roles = {"user", "agent", "supervisor", "admin"}
        
        assert role_names == expected_roles
    
    def test_rbac_permissions_categories(self):
        """Test that permissions cover all necessary categories"""
        permission_tags = set()
        for permission in RBAC_PERMISSIONS:
            permission_tags.update(permission["tags"])
        
        expected_categories = {
            "user", "management", "permissions", "roles", 
            "config", "system", "data", "reports", "agent"
        }
        
        assert expected_categories.issubset(permission_tags)
    
    def test_admin_has_all_permissions(self):
        """Test that admin role has all available permissions"""
        admin_role = next(role for role in RBAC_ROLES if role["name"] == "admin")
        all_permission_names = {p["name"] for p in RBAC_PERMISSIONS}
        admin_permissions = set(admin_role["default_permissions"])
        
        assert admin_permissions == all_permission_names

# Integration test fixtures
@pytest.fixture
def mock_tenant_db():
    """Mock tenant database for testing"""
    db = MagicMock()
    
    # Mock collections
    db.users = MagicMock()
    db.roles = MagicMock()
    db.permissions = MagicMock()
    db.config = MagicMock()
    
    return db

@pytest.fixture
def sample_user_data():
    """Sample user data for testing"""
    return {
        "username": "test_user",
        "password": "test123",
        "role": "agent",
        "email": "<EMAIL>",
        "created_by": "admin"
    }

class TestIntegration:
    """Integration tests for RBAC system"""
    
    @pytest.mark.asyncio
    async def test_full_user_creation_flow(self, mock_tenant_db, sample_user_data):
        """Test complete user creation flow"""
        # Setup mock responses
        mock_tenant_db.roles.find_one = AsyncMock(return_value={
            "name": "agent",
            "default_permissions": ["read_user", "write_data", "handle_cases"]
        })
        mock_tenant_db.users.insert_one = AsyncMock(return_value=MagicMock(inserted_id=ObjectId()))
        
        # Create user
        user_id = await create_user_with_role(sample_user_data, mock_tenant_db)
        
        # Verify calls
        assert user_id is not None
        mock_tenant_db.roles.find_one.assert_called_once()
        mock_tenant_db.users.insert_one.assert_called_once()
        
        # Verify user document structure
        call_args = mock_tenant_db.users.insert_one.call_args[0][0]
        assert call_args["username"] == sample_user_data["username"]
        assert call_args["role"] == sample_user_data["role"]
        assert "hashed_password" in call_args
        assert call_args["hashed_password"] != sample_user_data["password"]  # Should be hashed
