from pydantic import BaseModel, Field, field_validator, ConfigDict
from typing import Any, Dict, Literal, List, Optional
from pymongo.database import Database
# from src.reply.minio_client import MinIOClient,MinIOConfig

from .role import Role
from .permission import Permission

class User(BaseModel):
    """
    User document from the tenant database -> users collection
    """
    id: Any = Field(alias="_id")
    username: str
    role: Role
    permissions: List[Permission]


    @field_validator("id")
    def convert_objid_to_str(cls, value):
        return str(value)


    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        )
    # class Config:
    #     arbitrary_types_allowed = True

class UserTenantDB(BaseModel):
    tenant_id: str
    db: Database
    user: User

    model_config = ConfigDict(arbitrary_types_allowed=True)

    # class Config:
    #     arbitrary_types_allowed = True

    
class UserInvitation(BaseModel):
    username: str = Field(
        ...,
        json_schema_extra={"example": "user_username"}
    )
    role: Literal["admin", "supervisor", "agent", "user"] = Field(
        ...,
        description="Role to assign to the invited user"
    )
    email: str = Field(
        ...,
        json_schema_extra={"example": "<EMAIL>"}
    )

class UserRegistration(BaseModel):
    username: str = Field(
        ...,
        json_schema_extra={"example": "user_username"}
    )
    role: Literal["admin", "supervisor", "agent", "user"] = Field(
        ...,
        description="Role for the new user"
    )
    password: str = Field(
        ...,
        json_schema_extra={"example": "strongpassword123"}
    )
    email: str = Field(
        ...,
        json_schema_extra={"example": "<EMAIL>"}
    )
    token: str = Field(
        ...,
        json_schema_extra={"example": "invitation_token_here"}
    )

class UserCreate(BaseModel):
    username: str = Field(
        ...,
        description="Unique username for the user"
    )
    password: str = Field(
        ...,
        description="Password for the user"
    )
    role: Literal["admin", "supervisor", "agent", "user"] = Field(
        ...,
        description="Role to assign to the user"
    )
    email: str = Field(
        ...,
        description="Email address for the user"
    )
    is_active: bool = Field(
        default=True,
        description="Whether the user account is active"
    )

class UserUpdate(BaseModel):
    username: Optional[str] = Field(
        None,
        description="New username (optional)"
    )
    role: Optional[Literal["admin", "supervisor", "agent", "user"]] = Field(
        None,
        description="New role (optional)"
    )
    email: Optional[str] = Field(
        None,
        description="New email address (optional)"
    )
    is_active: Optional[bool] = Field(
        None,
        description="New active status (optional)"
    )
    permissions: Optional[List[str]] = Field(
        None,
        description="Custom permissions list (optional)"
    )

# Backward compatibility aliases
AgentInvitation = UserInvitation
AgentRegistration = UserRegistration
